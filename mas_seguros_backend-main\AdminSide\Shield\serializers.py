from datetime import datetime

from rest_framework import serializers
from django.contrib.auth.models import User
from Account import models as account_models
from Membership import models as membership_models
from Shield import models as shield_models
from Alert import models as alert_models
from Ticket import models as ticket_models
import logging
logger = logging.getLogger(__name__)


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Email already registered to other account! ")
        return lower_email

    class Meta:
        model = User
        fields = (
            'id',
            'first_name',
            'last_name',
            'username',
            'email',
            'password'
        )


class UserProfileSerializerForDashboard(serializers.ModelSerializer):
    user = UserSerializer()
    image_url = serializers.SerializerMethodField()

    def get_image_url(self, obj):
        """Get the full URL for the user profile image"""
        try:
            if obj.image:
                from mas_seguros_backend import settings as backend_setting
                return backend_setting.Base_url_path.format(url=obj.image.url)
            return None
        except:
            return None

    class Meta:
        model = account_models.UserProfile
        fields = ['user', 'firebase_uid', 'ui_id', 'phone', 'full_name', 'identification_card', 'birth_date', 'role',
                  'lat', 'long', 'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend',
                  'created_at', 'updated_at', 'image_url']


class PointOfInterestSerializerInShield(serializers.ModelSerializer):
    class Meta:
        model = shield_models.PointsOfInterest
        fields = "__all__"


class ShieldsSerializerForDashboard(serializers.ModelSerializer):
    members = UserProfileSerializerForDashboard(many=True)
    admin = UserProfileSerializerForDashboard(many=True)
    locations = PointOfInterestSerializerInShield(read_only=True)
    participent_count = serializers.SerializerMethodField()
    logo_url = serializers.SerializerMethodField()
    walkie_talkie_status = serializers.SerializerMethodField()

    def get_walkie_talkie_status(self, record):
        try:
            user = self.context['request'].user
            status = shield_models.WalkieTalkie.objects.filter(shield=record, member=user.userprofile).last()
            return status.listen_audio
        except:
            return None

    def get_logo_url(self, record):
        return record.logo_url

    def get_participent_count(self, record):
        record: shield_models.ShieldModel
        return record.members.all().count()

    class Meta:
        model = shield_models.ShieldModel
        fields = "__all__"


class PointOfInterestSerializer(serializers.ModelSerializer):
    # members = UserProfileSerializerForDashboard(many=True)

    admin = UserProfileSerializerForDashboard()

    class Meta:
        model = shield_models.PointsOfInterest
        fields = ['id', 'admin', 'poi_address', 'poi_lat', 'poi_long', 'poi_tag_name']


class ShieldMemberLocationSerializer(serializers.ModelSerializer):
    # members = UserProfileSerializerForDashboard(many=True)
    # admin = UserProfileSerializerForDashboard()

    class Meta:
        model = shield_models.Location
        fields = ['id', 'location', 'lat', 'long', 'lat_long', 'created_at', 'updated_at']


class ShieldMemberRouteSerializer(serializers.ModelSerializer):
    """
    Serializer for route data with optimized location fetching and error handling.
    
    Includes:
    - Location points for the route
    - Member information
    - Starting and ending POI details
    - Formatted time information
    """
    location = serializers.SerializerMethodField()
    member = UserProfileSerializerForDashboard()
    starting_poi = serializers.SerializerMethodField()
    ending_poi = serializers.SerializerMethodField()
    route_starting_time = serializers.SerializerMethodField()
    route_ending_time = serializers.SerializerMethodField()

    def get_location(self, obj):
        """Get all location points for this route - optimized to use prefetched data"""
        try:
            # Use prefetched data if available to avoid N+1 queries
            if hasattr(obj, '_prefetched_objects_cache') and 'location_set' in obj._prefetched_objects_cache:
                locations = obj.location_set.all()
            else:
                # Fallback to direct query if prefetch wasn't used
                locations = shield_models.Location.objects.filter(route=obj).order_by('created_at')

            return ShieldMemberLocationSerializer(locations, many=True).data
        except Exception as e:
            logger.warning(f"Error getting locations for route {obj.route_id}: {str(e)}")
            return []

    def get_starting_poi(self, obj):
        """Get starting POI name with fallback"""
        try:
            if obj.starting_poi:
                return obj.starting_poi.poi_tag_name
            return "Punto de inicio no especificado"
        except Exception as e:
            logger.warning(f"Error getting starting POI for route {obj.route_id}: {str(e)}")
            return "Punto de inicio no disponible"

    def get_ending_poi(self, obj):
        """Get ending POI name with fallback"""
        try:
            if obj.ending_poi:
                return obj.ending_poi.poi_tag_name
            return "Punto de destino no especificado"
        except Exception as e:
            logger.warning(f"Error getting ending POI for route {obj.route_id}: {str(e)}")
            return "Punto de destino no disponible"

    def get_route_starting_time(self, obj):
        """Format starting time as ISO string with date"""
        try:
            if obj.route_starting_time:
                # Combine with route date to create a full datetime
                from datetime import datetime
                route_date = obj.route_date if obj.route_date else datetime.now().date()
                full_datetime = datetime.combine(route_date, obj.route_starting_time)
                return full_datetime.isoformat()
            return None
        except Exception as e:
            logger.warning(f"Error formatting starting time for route {obj.route_id}: {str(e)}")
            return None

    def get_route_ending_time(self, obj):
        """Format ending time as ISO string with date"""
        try:
            if obj.route_ending_time:
                # Combine with route date to create a full datetime
                from datetime import datetime
                route_date = obj.route_date if obj.route_date else datetime.now().date()
                full_datetime = datetime.combine(route_date, obj.route_ending_time)
                return full_datetime.isoformat()
            return None
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Error formatting ending time for route {obj.route_id}: {str(e)}")
            return None

    class Meta:
        model = shield_models.Route
        fields = [
            'route_id', 'max_speed', 'minimum_phone_battery', 
            'starting_poi', 'ending_poi', 'route_completed',
            'route_starting_time', 'route_ending_time', 'route_date',
            'created_at', 'updated_at', 'member', 'location'
        ]


class AlertCategorySerialzier(serializers.ModelSerializer):
    class Meta:
        model = alert_models.AlertCategories
        fields = "__all__"


class ShieldAlertSerializer(serializers.ModelSerializer):
    userprofile = UserProfileSerializerForDashboard()
    category = AlertCategorySerialzier()

    class Meta:
        model = alert_models.AlertModel
        fields = "__all__"


class ShieldBiometricsSerializer(serializers.ModelSerializer):
    userprofile = UserProfileSerializerForDashboard()
    image_url = serializers.SerializerMethodField()

    def get_image_url(self, obj):
        try:
            if obj.image:
                from mas_seguros_backend import settings as backend_setting
                return backend_setting.Base_url_path.format(url=obj.image.url)
            return None
        except:
            return None

    class Meta:
        model = shield_models.Biometric
        fields = "__all__"


class GetShieldIdSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)


class GetMemberIDSerializer(serializers.Serializer):
    # shield_id = serializers.IntegerField(required=True)
    member_id = serializers.IntegerField(required=True)
    month = serializers.CharField(required=False)
    year = serializers.CharField(required=False)


class GetShieldIdAndMemberIDSerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)
    member_id = serializers.IntegerField(required=False)


class ShieldMemberRoutesRequestSerializer(serializers.Serializer):
    """
    Serializer for validating route history request parameters.
    
    Supports filtering by shield_id, member_id, and optional date/month/year filters.
    Date parameter takes precedence over month/year filtering.
    """
    shield_id = serializers.IntegerField(
        required=True, 
        min_value=1,
        error_messages={
            'required': 'ID del escudo es requerido',
            'min_value': 'ID del escudo debe ser mayor a 0',
            'invalid': 'ID del escudo debe ser un número válido'
        }
    )
    member_id = serializers.IntegerField(
        required=True, 
        min_value=1,
        error_messages={
            'required': 'ID del miembro es requerido',
            'min_value': 'ID del miembro debe ser mayor a 0',
            'invalid': 'ID del miembro debe ser un número válido'
        }
    )
    month = serializers.IntegerField(
        required=False, 
        min_value=1, 
        max_value=12,
        error_messages={
            'min_value': 'El mes debe estar entre 1 y 12',
            'max_value': 'El mes debe estar entre 1 y 12',
            'invalid': 'El mes debe ser un número válido'
        }
    )
    year = serializers.IntegerField(
        required=False, 
        min_value=2020, 
        max_value=2030,
        error_messages={
            'min_value': 'El año debe ser mayor a 2019',
            'max_value': 'El año debe ser menor a 2031',
            'invalid': 'El año debe ser un número válido'
        }
    )
    date = serializers.DateField(
        required=False,
        error_messages={
            'invalid': 'Formato de fecha inválido. Use YYYY-MM-DD'
        }
    )

    def validate(self, data):
        """Custom validation for date filtering parameters"""
        month = data.get('month')
        year = data.get('year')
        date = data.get('date')

        # If both date and month/year are provided, date takes precedence
        if date and (month or year):
            # Remove month/year if date is provided to avoid conflicts
            data.pop('month', None)
            data.pop('year', None)

        # If month is provided without year, use current year
        elif month and not year:
            from datetime import datetime
            data['year'] = datetime.now().year

        # Validate date is not in the future
        if date:
            from datetime import date as date_class
            if date > date_class.today():
                raise serializers.ValidationError({
                    'date': 'La fecha no puede ser futura'
                })

        return data


class GetMonthlySerializer(serializers.Serializer):
    month = serializers.CharField(required=True)


class sheildMembershipSerializer(serializers.ModelSerializer):
    class Meta:
        model = membership_models.MembershipModel
        fields = "__all__"


class getMemberIdSerializer(serializers.Serializer):
    member_id = serializers.ListField(required=True)
    date = serializers.DateField(required=False)
    report_type = serializers.CharField(max_length=20, required=True)


#  Raazi hu Malik phir teri raza mai
#  Likhi ho qismat mai phir manzil ya mout!!!


class HierarchySerializerShield(serializers.ModelSerializer):
    member = UserProfileSerializerForDashboard()
    # shield = ShieldsSerializerInCreateShield()
    admin = serializers.SerializerMethodField()

    def get_admin(self, record):
        record: shield_models.Hierarchie
        shield = shield_models.ShieldModel.objects.filter(id=record.shield.id, admin=record.member)
        if shield:
            return True
        return False

    class Meta:
        model = shield_models.Hierarchie
        fields = ['member', 'hierarchy', 'admin']


class SuspendSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    suspended = serializers.CharField(max_length=10, required=True)
