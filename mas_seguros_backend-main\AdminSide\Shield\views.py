import csv
import io
import string
import random
from mas_seguros_backend import settings as backend_setting
from django.shortcuts import HttpResponse
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from rest_framework.generics import get_object_or_404
from Account import models as account_models
from Membership import models as membership_models
from . import serializers as shield_serialziers
from Shield import models as shield_models
from Alert import models as alert_models
from django.db.models import Sum
from rest_framework.parsers import JSONParser
from django.views.decorators.csrf import csrf_exempt
from Ticket import models as ticket_models
from django.http import JsonResponse
from django.http import FileResponse
from reportlab.pdfgen import canvas
from django.core.files.storage import default_storage
from django.http import HttpResponse
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import inch
from rest_framework.permissions import AllowAny
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from Sos import models as sos_models
from Sos.serializer import SosDetailSerializer
import logging
logger = logging.getLogger(__name__)


# Create your views here.


class AllShields(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        all_shields = shield_models.ShieldModel.objects.all()
        shield_serializer = shield_serialziers.ShieldsSerializerForDashboard(all_shields, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=shield_serializer.data,
                                           msg='All shields'), status.HTTP_200_OK)


class ShieldPointOfInterest(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        if id == 0:
            point_of_interest = shield_models.PointsOfInterest.objects.all()
            point_of_interest_serializer = shield_serialziers.PointOfInterestSerializer(point_of_interest, many=True)
            return JsonResponse(point_of_interest_serializer.data, safe=False)
        shield = shield_models.ShieldModel.objects.filter(id=id).last()
        if shield:
            point_of_interest = shield.locations.all()
            point_of_interest_serializer = shield_serialziers.PointOfInterestSerializer(point_of_interest, many=True)
            return JsonResponse(point_of_interest_serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=point_of_interest_serializer.data,
            #                                msg='All point of Interests of this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldMembers(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=id).last()

        # Check if shield exists
        if not shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No shield found with this ID'),
                status.HTTP_400_BAD_REQUEST)

        # Get hierarchies for this shield
        hierarchy = shield_models.Hierarchie.objects.filter(shield=shield)

        # Return data whether hierarchies exist or not
        hierarchy_serializer = shield_serialziers.HierarchySerializerShield(hierarchy, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=hierarchy_serializer.data,
                                           msg='Members with hierarchies of this shield'),
            status.HTTP_200_OK)


class ShieldMemberLocations(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetMemberIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        # shield_id = request.data.get('shield_id')
        member_id = request.query_params.get('member_id')
        # shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if member_id:
            locations = shield_models.Location.objects.filter(userprofile__user_id=member_id)
            # routes = routes.filter(member_id=member_id)
            members_serializer = shield_serialziers.ShieldMemberLocationSerializer(locations, many=True)
            return JsonResponse(members_serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=members_serializer.data,
            #                                msg='All location of this member'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Member of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldMemberRoutes(APIView):
    """
    API endpoint to retrieve route history for shield members.
    
    Supports filtering by:
    - shield_id (required): ID of the shield
    - member_id (required): ID of the member  
    - date (optional): Specific date in YYYY-MM-DD format
    - month (optional): Month filter (1-12)
    - year (optional): Year filter
    
    Returns route data with location points, POI information, and timing details.
    Limited to 100 most recent routes for performance.
    """
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldMemberRoutesRequestSerializer

    def get(self, request):
        try:
            # Validate request parameters
            serializer = self.serializer_class(data=request.query_params)
            if not serializer.is_valid():
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='Parámetros de solicitud inválidos',
                        errors=serializer.errors
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get validated parameters
            validated_data = serializer.validated_data
            shield_id = validated_data.get('shield_id')
            member_id = validated_data.get('member_id')
            month = validated_data.get('month', None)
            year = validated_data.get('year', None)
            date = validated_data.get('date', None)

            # Validate that the shield exists
            try:
                shield = shield_models.ShieldModel.objects.get(id=shield_id)
            except shield_models.ShieldModel.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='Escudo no encontrado'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Validate that the member exists and belongs to the shield
            try:
                member = shield.members.get(user_id=member_id)
            except account_models.UserProfile.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='Miembro no encontrado en este escudo'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Build optimized query with proper joins and prefetching
            routes_query = shield_models.Route.objects.filter(
                shield_id=shield_id,
                member__user_id=member_id
            ).select_related(
                'member__user',
                'shield',
                'starting_poi',
                'ending_poi'
            ).prefetch_related(
                'location_set'  # Prefetch related locations to avoid N+1 queries
            )

            # Filter by date parameters (date takes precedence over month/year)
            if date:
                routes_query = routes_query.filter(route_date=date)
            elif month and year:
                routes_query = routes_query.filter(route_date__month=month, route_date__year=year)
            elif month:
                routes_query = routes_query.filter(route_date__month=month)
            elif year:
                routes_query = routes_query.filter(route_date__year=year)

            # Order by route date (most recent first) and limit results for performance
            routes = routes_query.order_by('-route_date', '-created_at')[:100]

            # Serialize the data
            routes_serializer = shield_serialziers.ShieldMemberRouteSerializer(routes, many=True)

            # Return data as JsonResponse for frontend compatibility
            return JsonResponse(routes_serializer.data, safe=False)

        except ValueError as e:
            # Handle value conversion errors
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg=f'Error en los parámetros: {str(e)}'
                ),
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error in ShieldMemberRoutes: {str(e)}", exc_info=True)
            
            # Return proper error response
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg='Error interno del servidor al obtener los datos de rutas'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ShieldAlertAndSos(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        try:
            serializer = self.serializer_class(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            shield_id = request.query_params.get('id')
            
            if not shield_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='Shield ID is required'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
            
            if not shield:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='No Shield found with this ID'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get alerts for this shield
            alerts = shield.alert.all()
            alerts_serializer = shield_serialziers.ShieldAlertSerializer(alerts, many=True)
            
            # Get SOS records for this shield with optimized queries
            sos_records = sos_models.Sos.objects.filter(shield=shield).select_related(
                'sender'
            ).prefetch_related(
                'soscomment_set',
                'sosevidence_set'
            ).order_by('-created_at')
            sos_serializer = SosDetailSerializer(sos_records, many=True)
            
            # Transform alerts data
            transformed_alerts = []
            for alert in alerts_serializer.data:
                try:
                    # Ensure evidence_url is properly formatted
                    evidence_url = alert.get('evidence_url')
                    if evidence_url and not evidence_url.startswith('http'):
                        # If evidence_url doesn't start with http, it might be a relative path
                        from mas_seguros_backend import settings as backend_setting
                        if hasattr(backend_setting, 'Base_url_path'):
                            evidence_url = backend_setting.Base_url_path.format(url=evidence_url)

                    transformed_alerts.append({
                        'id': alert.get('id', ''),
                        'type': 'alert',
                        'category': alert.get('category', ''),
                        'status': alert.get('status_name', ''),
                        'alert_date': alert.get('alert_date', ''),
                        'alert_time': alert.get('alert_time', ''),
                        'evidence_url': evidence_url,
                        'evidence_number': f"A{alert.get('id', '')}",
                        'description': alert.get('description', 'No hay comentarios disponibles'),
                        'userprofile': {
                            'full_name': alert.get('userprofile', {}).get('full_name', 'Usuario'),
                            'phone': alert.get('userprofile', {}).get('phone'),
                            'user': alert.get('userprofile', {}).get('user', {'id': alert.get('id', '')})
                        },
                        'lat': alert.get('lat'),
                        'long': alert.get('long'),
                        'created_at': alert.get('created_at')
                    })
                except Exception as e:
                    print(f"Error transforming alert {alert.get('id', 'unknown')}: {str(e)}")
                    continue

            # Transform SOS data
            transformed_sos = []
            for sos_item in sos_serializer.data:
                try:
                    # Handle the case where sos_item might be an integer
                    if isinstance(sos_item, int):
                        sos_obj = sos_models.Sos.objects.get(id=sos_item)
                        sos_item = SosDetailSerializer(sos_obj).data

                    # Use the properly formatted date and time from SOS serializer
                    alert_date = sos_item.get('alert_date', '')
                    alert_time = sos_item.get('alert_time', '')

                    # Fallback to alert_datetime if the above are not available
                    if not alert_date or not alert_time:
                        alert_datetime = sos_item.get('alert_datetime', '')
                        if alert_datetime:
                            alert_date = alert_datetime.split('T')[0] if not alert_date else alert_date
                            alert_time = alert_datetime.split('T')[1].split('.')[0] if not alert_time else alert_time

                    sender = sos_item.get('sender', {})
                    if isinstance(sender, int):
                        sender_obj = account_models.UserProfile.objects.get(id=sender)
                        sender = {
                            'full_name': sender_obj.full_name if hasattr(sender_obj, 'full_name') else 'Usuario',
                            'phone': sender_obj.phone if hasattr(sender_obj, 'phone') else None,
                            'user': {
                                'id': sender_obj.user.id if hasattr(sender_obj, 'user') else sos_item.get('id', ''),
                                'first_name': sender_obj.user.first_name if hasattr(sender_obj, 'user') and hasattr(sender_obj.user, 'first_name') else '',
                                'last_name': sender_obj.user.last_name if hasattr(sender_obj, 'user') and hasattr(sender_obj.user, 'last_name') else '',
                                'email': sender_obj.user.email if hasattr(sender_obj, 'user') and hasattr(sender_obj.user, 'email') else ''
                            }
                        }

                    # Ensure evidence_url is properly formatted for SOS
                    evidence_url = sos_item.get('evidence_url') or sos_item.get('evidence')
                    if evidence_url and not evidence_url.startswith('http'):
                        # If evidence_url doesn't start with http, it might be a relative path
                        from mas_seguros_backend import settings as backend_setting
                        if hasattr(backend_setting, 'Base_url_path'):
                            evidence_url = backend_setting.Base_url_path.format(url=evidence_url)

                    transformed_sos.append({
                        'id': sos_item.get('id', ''),
                        'type': 'SOS',
                        'category': 'SOS',
                        'status': sos_item.get('status', ''),
                        'alert_date': alert_date,
                        'alert_time': alert_time,
                        'evidence_url': evidence_url,
                        'evidence_number': sos_item.get('evidence_number', f"S{sos_item.get('id', '')}"),
                        'description': sos_item.get('description', 'No hay comentarios disponibles para este SOS'),
                        'userprofile': sender,
                        'lat': sos_item.get('lat'),
                        'long': sos_item.get('long'),
                        'created_at': sos_item.get('created_at')
                    })
                except Exception as e:
                    print(f"Error transforming SOS {sos_item.get('id', 'unknown')}: {str(e)}")
                    continue

            # Combine both alerts and SOS records
            combined_data = {
                'alerts': transformed_alerts,
                'sos': transformed_sos
            }
            
            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=combined_data,
                    msg='All alerts and SOS for this shield'
                )
            )
        except Exception as e:
            print(f"Error in ShieldAlertAndSos: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving shield alerts and SOS: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ShieldBiometrics(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            biometrics = shield_models.Biometric.objects.filter(shield=shield)
            print("this is the list of routes===", biometrics)
            routes_serializer = shield_serialziers.ShieldBiometricsSerializer(biometrics, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=routes_serializer.data,
                                               msg='All biometrics of this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


@csrf_exempt
def update_sheild(request, pk):
    try:
        photographer_id = shield_models.ShieldModel.objects.get(pk=pk)
    except photographer_id.DoesNotExist:
        return HttpResponse(status=404)
    if request.method == "PATCH":
        data = JSONParser().parse(request)
        serializer = shield_serialziers.ShieldsSerializerForDashboard(photographer_id, data=data)
        if serializer.is_valid():
            serializer.save()
            return JsonResponse(serializer.data)
        return JsonResponse(serializer.errors, status=400)


class sheild_get_single(APIView):
    ...

    def get(self, request, id=None):
        if id:
            item = shield_models.ShieldModel.objects.get(id=id)
            serializer = shield_serialziers.ShieldsSerializerForDashboard(item)
            return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class sheild_membership(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            biometrics = membership_models.MembershipModel.objects.filter(shield=shield)
            print("this is the list of routes===", biometrics)
            routes_serializer = shield_serialziers.sheildMembershipSerializer(biometrics, many=True)
            return JsonResponse(routes_serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=routes_serializer.data,
            #                                msg='All biometrics of this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class AllBiometrics(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        biometrics = shield_models.Biometric.objects.all()
        if biometrics:
            routes_serializer = shield_serialziers.ShieldBiometricsSerializer(biometrics, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, msg="all biometrics",
                                               data=routes_serializer.data), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Biometrics available'), status.HTTP_400_BAD_REQUEST)


class UserBiometrics(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.getMemberIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_id = request.data.get('member_id')
        userprofile = account_models.UserProfile.objects.filter(user_id=user_id).last()
        biometrics = shield_models.Biometric.objects.filter(userprofile=userprofile).select_related(
            'userprofile__user',
            'shield'
        ).order_by('-created_at')
        if biometrics:
            routes_serializer = shield_serialziers.ShieldBiometricsSerializer(biometrics, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, msg="all biometrics",
                                               data=routes_serializer.data), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, msg="No biometrics available",
                                               data=[]), status.HTTP_200_OK)


class GetUserBiometricsOptimized(APIView):
    """
    Optimized endpoint to get all biometric records for a specific user
    This replaces the need to fetch from multiple shields separately
    """
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            user_id = request.query_params.get('user_id')
            if not user_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='user_id parameter is required'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get user profile
            try:
                userprofile = account_models.UserProfile.objects.get(user_id=user_id)
            except account_models.UserProfile.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='User not found'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get all biometric records for this user with optimized queries
            biometrics = shield_models.Biometric.objects.filter(
                userprofile=userprofile
            ).select_related(
                'userprofile__user',
                'shield'
            ).order_by('-created_at')

            # Serialize the data
            serializer = shield_serialziers.ShieldBiometricsSerializer(biometrics, many=True)

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=serializer.data,
                    msg=f'Retrieved {len(serializer.data)} biometric records for user'
                )
            )

        except Exception as e:
            print(f"Error in GetUserBiometricsOptimized: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving user biometric data: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DownloadUserBiometrics(APIView):
    permission_classes = (AllowAny,)
    serializer_class = shield_serialziers.getMemberIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        users_id = request.data.get('member_id')

        date = request.data.get('date')
        report_type = request.data.get('report_type')
        print("this is user id ===", users_id)
        downloadable_list = []
        for user_id in users_id:
            userprofile = account_models.UserProfile.objects.filter(user_id=user_id).last()
            if date:
                biometrics = shield_models.Biometric.objects.filter(userprofile=userprofile, created_at__date=date)
            else:
                biometrics = shield_models.Biometric.objects.filter(userprofile=userprofile)
            if report_type == 'pdf':
                queryset = biometrics
                print("this is the queryset===", queryset)
                buffer = io.BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=letter)
                styles = getSampleStyleSheet()
                style_heading = styles["Heading1"]
                style_body = styles["Normal"]
                data = []
                headings = ['User', 'Biometric Code', 'Shield', 'Address', 'Type']
                data.append(headings)
                for obj in queryset:
                    row = []
                    row.append(obj.userprofile.user.get_full_name())
                    row.append(obj.biometric_code)
                    row.append(obj.shield.shield_name)
                    row.append(obj.address)
                    row.append(obj.type)
                    data.append(row)
                table = Table(data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), '#c8c8c8'),
                    ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
                    ('TEXTCOLOR', (0, 1), (-1, -1), '#000000'),
                    ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 12),
                    ('BOTTOMPADDING', (0, 1), (-1, -1), 10),
                ]))
                elements = []
                elements.append(Paragraph("Biometric Report", style_heading))
                elements.append(Spacer(1, 0.25 * inch))
                elements.append(table)
                doc.build(elements)
                file_name = 'biometric_reports/adminside/{}'.format(''.join(random.choices(string.ascii_uppercase +
                                                                                           string.digits,
                                                                                           k=7)) + '.pdf')
                print("this is filename====", file_name)
                default_storage.save(file_name, buffer)
                file_url = default_storage.url(file_name)
                downloadable_list.append(backend_setting.Base_url_path.format(url=file_url))
            if report_type == 'csv':
                file_name = ''.join(random.choices(string.ascii_uppercase +
                                                   string.digits, k=7)) + '.csv'
                with open(file_name, 'w', newline='') as file:
                    writer = csv.writer(file)
                    writer.writerow(
                        ["Shield_name", "Member_name", "Biometric_code", "Image", "lat", "long", "address", "Type"])
                    for biometric in biometrics:
                        writer.writerow(
                            [biometric.shield.shield_name, biometric.userprofile.name, biometric.biometric_code,
                             biometric.image_url, biometric.lat, biometric.long, biometric.address, biometric.type])
                file = '/' + file_name
                downloadable_list.append(backend_setting.Base_url_path.format(url=file))

        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, msg="all biometrics",
                                           data=downloadable_list), status.HTTP_200_OK)


class SuspendShield(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.SuspendSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        shield_id = request.data.get('id')
        suspended = request.data.get('suspended')
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            if suspended.lower() == "true":
                shield_obj.suspend = True
            elif suspended.lower() == "false":
                shield_obj.suspend = False
            shield_obj.save()
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               msg='Shield suspended Status Updated'), status.HTTP_200_OK)
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='Shield not found'), status.HTTP_400_BAD_REQUEST)


class PointOfInterestVisitHistory(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            poi_id = request.query_params.get('poi_id')
            date_filter = request.query_params.get('date')

            if not poi_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='poi_id parameter is required'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the point of interest
            try:
                poi = shield_models.PointsOfInterest.objects.get(id=poi_id)
            except shield_models.PointsOfInterest.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='Point of interest not found'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get visit history for this POI
            visits = shield_models.Location.objects.filter(
                point_of_interest=poi
            ).select_related('userprofile', 'userprofile__user').order_by('-created_at')

            # Apply date filter if provided
            if date_filter:
                try:
                    from datetime import datetime
                    filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
                    visits = visits.filter(created_at__date=filter_date)
                except ValueError:
                    return Response(
                        backend_utils.failure_response(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            msg='Invalid date format. Use YYYY-MM-DD'
                        ),
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Serialize the visit data
            visit_data = []
            for visit in visits:
                visit_data.append({
                    'id': visit.id,
                    'member_name': visit.userprofile.full_name if visit.userprofile else 'Unknown',
                    'member_id': visit.userprofile.id if visit.userprofile else None,
                    'date': visit.created_at.strftime('%d/%m/%Y') if visit.created_at else '',
                    'time': visit.created_at.strftime('%H:%M') if visit.created_at else '',
                    'created_at': visit.created_at.isoformat() if visit.created_at else None,
                })

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=visit_data,
                    msg='Point of interest visit history retrieved successfully'
                )
            )

        except Exception as e:
            print(f"Error in PointOfInterestVisitHistory: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving visit history: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DownloadRouteHistory(APIView):
    permission_classes = (AllowAny,)

    def get(self, request):
        try:
            # Get parameters from request
            shield_id = request.query_params.get('shield_id')
            member_id = request.query_params.get('member_id')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            format_type = request.query_params.get('format', 'xlsx')

            # Validate required parameters
            if not shield_id:
                return JsonResponse({'error': 'shield_id is required'}, status=400)

            # Get shield
            try:
                shield = shield_models.ShieldModel.objects.get(id=shield_id)
            except shield_models.ShieldModel.DoesNotExist:
                return JsonResponse({'error': 'Shield not found'}, status=404)

            # Build query for routes
            routes_query = shield_models.Route.objects.filter(shield=shield)

            # Filter by member if specified
            if member_id:
                routes_query = routes_query.filter(member__user_id=member_id)

            # Filter by date range if specified
            if start_date:
                routes_query = routes_query.filter(route_date__gte=start_date)
            if end_date:
                routes_query = routes_query.filter(route_date__lte=end_date)

            # Get routes with related data
            routes = routes_query.select_related('member__user', 'shield').order_by('-route_date')

            if not routes.exists():
                return JsonResponse({'error': 'No routes found for the specified criteria'}, status=404)

            # Generate file based on format
            if format_type.lower() == 'pdf':
                return self._generate_pdf(routes, shield)
            else:
                return self._generate_excel(routes, shield)

        except Exception as e:
            print(f"Error in DownloadRouteHistory: {str(e)}")
            import traceback
            traceback.print_exc()
            return JsonResponse({'error': f'Error generating route history: {str(e)}'}, status=500)

    def _generate_excel(self, routes, shield):
        """Generate Excel file for route history"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment
            import io

            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Route History"

            # Add headers
            headers = [
                'Route ID', 'Member Name', 'Date', 'Start Time', 'End Time',
                'Max Speed (km/h)', 'Min Battery (%)', 'Route Completed', 'Locations Count'
            ]

            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # Add data rows
            for row, route in enumerate(routes, 2):
                try:
                    locations_count = shield_models.Location.objects.filter(route=route).count()

                    ws.cell(row=row, column=1, value=route.route_id or 'N/A')
                    ws.cell(row=row, column=2, value=route.member.full_name if route.member else 'N/A')
                    ws.cell(row=row, column=3, value=route.route_date.strftime('%d/%m/%Y') if route.route_date else 'N/A')
                    ws.cell(row=row, column=4, value=route.route_starting_time.strftime('%H:%M') if route.route_starting_time else 'N/A')
                    ws.cell(row=row, column=5, value=route.route_ending_time.strftime('%H:%M') if route.route_ending_time else 'N/A')
                    ws.cell(row=row, column=6, value=route.max_speed or 'N/A')
                    ws.cell(row=row, column=7, value=route.minimum_phone_battery or 'N/A')
                    ws.cell(row=row, column=8, value='Yes' if route.route_completed else 'No')
                    ws.cell(row=row, column=9, value=locations_count)
                except Exception as e:
                    print(f"Error processing route {route.route_id}: {str(e)}")
                    # Add error row
                    ws.cell(row=row, column=1, value=f"Error: {route.route_id}")
                    for col in range(2, len(headers) + 1):
                        ws.cell(row=row, column=col, value='Error')

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # Save to BytesIO
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            # Create response
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="route_history_shield_{shield.id}.xlsx"'
            return response

        except ImportError as e:
            print(f"Missing dependency for Excel generation: {str(e)}")
            return JsonResponse({'error': 'Excel generation not available. Missing openpyxl dependency.'}, status=500)
        except Exception as e:
            print(f"Error generating Excel file: {str(e)}")
            return JsonResponse({'error': f'Error generating Excel file: {str(e)}'}, status=500)

    def _generate_pdf(self, routes, shield):
        """Generate PDF file for route history"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib import colors
            import io

            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Title
            title = Paragraph(f"Route History - {shield.shield_name}", styles['Title'])
            story.append(title)
            story.append(Spacer(1, 12))

            # Table data
            data = [['Route ID', 'Member', 'Date', 'Start Time', 'Max Speed', 'Battery', 'Completed']]

            for route in routes:
                try:
                    data.append([
                        route.route_id or 'N/A',
                        route.member.full_name if route.member else 'N/A',
                        route.route_date.strftime('%d/%m/%Y') if route.route_date else 'N/A',
                        route.route_starting_time.strftime('%H:%M') if route.route_starting_time else 'N/A',
                        f"{route.max_speed} km/h" if route.max_speed else 'N/A',
                        f"{route.minimum_phone_battery}%" if route.minimum_phone_battery else 'N/A',
                        'Yes' if route.route_completed else 'No'
                    ])
                except Exception as e:
                    print(f"Error processing route {route.route_id} for PDF: {str(e)}")
                    data.append([f"Error: {route.route_id}", 'Error', 'Error', 'Error', 'Error', 'Error', 'Error'])

            # Create table
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)
            doc.build(story)

            buffer.seek(0)
            response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="route_history_shield_{shield.id}.pdf"'
            return response

        except ImportError as e:
            print(f"Missing dependency for PDF generation: {str(e)}")
            return JsonResponse({'error': 'PDF generation not available. Missing reportlab dependency.'}, status=500)
        except Exception as e:
            print(f"Error generating PDF file: {str(e)}")
            return JsonResponse({'error': f'Error generating PDF file: {str(e)}'}, status=500)
